import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    // 创建一个模拟的认证token
    const response = NextResponse.json({
      success: true,
      message: 'Guest login successful'
    })

    // 设置认证cookie
    response.cookies.set('auth-token', 'guest-token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7 // 7 days
    })

    return response
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: 'Guest login failed'
    }, { status: 500 })
  }
}
