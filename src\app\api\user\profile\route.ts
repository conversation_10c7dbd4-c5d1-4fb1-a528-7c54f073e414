import { NextRequest, NextResponse } from 'next/server'

// 模拟用户配置数据存储
const userProfiles = new Map()

export async function GET(request: NextRequest) {
  try {
    // 检查是否有登录cookie或session
    const cookies = request.cookies
    const isLoggedIn = cookies.get('auth-token') || cookies.get('user-session')
    
    if (!isLoggedIn) {
      return NextResponse.json({
        success: false,
        message: 'Authentication required'
      }, { status: 401 })
    }

    // 获取用户配置（这里使用模拟数据）
    const userId = 'mock-user-id' // 实际应用中应该从认证信息中获取
    const profile = userProfiles.get(userId) || {
      socialMedia: {
        bilibili: '',
        xiaohongshu: '',
        weibo: ''
      }
    }

    return NextResponse.json({
      success: true,
      data: profile
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    // 检查是否有登录cookie或session
    const cookies = request.cookies
    const isLoggedIn = cookies.get('auth-token') || cookies.get('user-session')
    
    if (!isLoggedIn) {
      return NextResponse.json({
        success: false,
        message: 'Authentication required'
      }, { status: 401 })
    }

    const body = await request.json()
    const { socialMedia } = body

    // 验证数据格式
    if (!socialMedia || typeof socialMedia !== 'object') {
      return NextResponse.json({
        success: false,
        message: 'Invalid data format'
      }, { status: 400 })
    }

    // 保存用户配置（这里使用模拟数据存储）
    const userId = 'mock-user-id' // 实际应用中应该从认证信息中获取
    const profile = {
      socialMedia: {
        bilibili: socialMedia.bilibili || '',
        xiaohongshu: socialMedia.xiaohongshu || '',
        weibo: socialMedia.weibo || ''
      }
    }
    
    userProfiles.set(userId, profile)

    return NextResponse.json({
      success: true,
      data: profile,
      message: 'Profile updated successfully'
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 })
  }
}
